'use client'

import React, { useState } from 'react'
import { useAppStore, useActiveTab } from '@/lib/store'
import { X, Plus } from 'lucide-react'
import LightBrowser from '@/components/ui/LightBrowser'

const WorkArea: React.FC = () => {
  const { tabs, activeTabId, setActiveTab, removeTab, addTab, setProcessing } = useAppStore()
  const activeTab = useActiveTab()
  const [inputValue, setInputValue] = useState('')

  // 处理输入提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    setProcessing(true)

    try {
      // 判断输入类型并规范化URL
      let processedInput = inputValue.trim()
      let isUrl = false

      // 检测和规范化URL
      if (processedInput.startsWith('http://') || processedInput.startsWith('https://')) {
        isUrl = true
      } else if (processedInput.includes('.') && !processedInput.includes(' ') && processedInput.length > 3) {
        // 可能是没有协议的URL，自动添加https://
        processedInput = 'https://' + processedInput
        isUrl = true
      }

      if (isUrl) {
        // 对于URL：立即创建标签页并显示浏览器，同时后台处理AI分析
        const tabId = addTab({
          title: new URL(processedInput).hostname,
          sourceType: 'url',
          sourceData: processedInput,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false, // 立即显示浏览器，不需要等待
          aiAnalyzing: true // 新增状态：表示AI正在后台分析
        })

        setInputValue('')
        setProcessing(false)

        // 后台异步处理AI分析
        processUrlInBackground(processedInput, tabId)
      } else {
        // 对于文本：使用流式处理
        const tabId = addTab({
          title: '文本内容',
          sourceType: 'text',
          sourceData: inputValue,
          originalContent: '',
          aiNoteMarkdown: '',
          isLoading: false,
          aiAnalyzing: true // 流式分析
        })

        setInputValue('')
        setProcessing(false)

        // 后台流式处理文本内容
        processContentWithStream(processedInput, 'text', tabId)
      }
    } catch (error) {
      console.error('处理错误:', error)
      setProcessing(false)
      // 可以在这里显示错误提示
    }
  }

  // 流式处理内容分析（支持URL和文本）
  const processContentWithStream = async (input: string, type: 'url' | 'text', tabId: string) => {
    try {
      console.log('开始流式AI分析:', input)

      const response = await fetch('/api/process/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input,
          type
        }),
        signal: AbortSignal.timeout(60000) // 60秒超时，给AI生成更多时间
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      let accumulatedAINote = ''
      const decoder = new TextDecoder()
      let basicInfoReceived = false

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            console.log('收到SSE数据:', data)
            if (data === '[DONE]') {
              // 完成流式更新
              const finalTab = useAppStore.getState().tabs.find(t => t.id === tabId)
              useAppStore.getState().updateTab(tabId, {
                aiNoteMarkdown: finalTab?.aiNoteMarkdown || accumulatedAINote,
                aiAnalyzing: false
              })
              console.log('流式AI分析完成:', accumulatedAINote.length, '字符')
              return
            }

            try {
              const parsed = JSON.parse(data)
              console.log('解析的数据:', parsed)
              
              if (parsed.type === 'basic_info') {
                // 处理基本信息
                const basicInfo = parsed.data
                useAppStore.getState().updateTab(tabId, {
                  title: basicInfo.title || (type === 'url' ? new URL(input).hostname : '文本内容'),
                  originalContent: basicInfo.content
                })
                basicInfoReceived = true
                
              } else if (parsed.type === 'ai_note' && basicInfoReceived) {
                // 处理结构化笔记流式更新
                const noteData = parsed.data
                console.log('收到结构化笔记数据:', noteData)

                if (noteData.content) {
                  accumulatedAINote += noteData.content
                  console.log('累积结构化笔记长度:', accumulatedAINote.length, '最新内容:', noteData.content)
                  // 实时更新结构化笔记
                  useAppStore.getState().updateAINoteStream(tabId, accumulatedAINote)
                }

                if (noteData.isComplete) {
                  console.log('结构化笔记生成完成，最终内容长度:', (noteData.fullContent || accumulatedAINote).length)
                  // 确保最终内容完整
                  useAppStore.getState().updateTab(tabId, {
                    aiNoteMarkdown: noteData.fullContent || accumulatedAINote,
                    aiAnalyzing: false
                  })
                }
              }
            } catch (e) {
              // 忽略解析错误，继续处理下一行
            }
          }
        }
      }
    } catch (error) {
      console.error('流式结构化分析失败:', error)

      // 更详细的错误处理
      let errorMessage = '分析失败'
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = '请求超时，请稍后重试'
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = '网络连接失败，请检查网络'
        } else {
          errorMessage = error.message
        }
      }

      // 更新状态并显示错误信息
      useAppStore.getState().updateTab(tabId, {
        aiAnalyzing: false,
        error: errorMessage
      })
    }
  }

  // 后台处理URL的AI分析（保持向后兼容）
  const processUrlInBackground = async (url: string, tabId: string) => {
    await processContentWithStream(url, 'url', tabId)
  }

  // 如果没有标签页，显示输入界面
  if (tabs.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="max-w-3xl w-full px-8">
          {/* 极简标题区域 */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl mb-6 shadow-lg">
              <span className="text-2xl">✨</span>
            </div>
            <h1 className="text-4xl font-light text-gray-900 mb-3 tracking-tight">
              沉淀
            </h1>
            <p className="text-lg text-gray-500 font-light">
              智能内容分析与结构化笔记生成
            </p>
          </div>

          {/* 现代化输入区域 */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="relative group">
              <textarea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="输入网页链接或粘贴文本内容..."
                className="w-full h-32 px-6 py-4 bg-white border border-gray-200 rounded-2xl resize-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 outline-none text-base transition-all duration-200 shadow-sm hover:shadow-md group-hover:border-gray-300"
                disabled={useAppStore.getState().isProcessing}
              />
              <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                <div className="text-xs text-gray-400">
                  {inputValue.length > 0 && `${inputValue.length} 字符`}
                </div>
              </div>
            </div>

            <div className="flex justify-center">
              <button
                type="submit"
                disabled={!inputValue.trim() || useAppStore.getState().isProcessing}
                className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
              >
                {useAppStore.getState().isProcessing ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>处理中...</span>
                  </div>
                ) : (
                  '开始分析'
                )}
              </button>
            </div>
          </form>

          {/* 功能特性提示 */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="p-4">
              <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600">🌐</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-1">智能提取</h3>
              <p className="text-sm text-gray-500">自动提取网页核心内容</p>
            </div>
            <div className="p-4">
              <div className="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <span className="text-purple-600">📝</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-1">结构化笔记</h3>
              <p className="text-sm text-gray-500">生成清晰的结构化摘要</p>
            </div>
            <div className="p-4">
              <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600">💬</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-1">智能对话</h3>
              <p className="text-sm text-gray-500">基于内容的深度问答</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* 现代化标签页栏 */}
      <div className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 flex items-center px-6 shadow-sm">
        <div className="flex-1 flex items-center space-x-2 overflow-x-auto">
          {tabs.map((tab) => (
            <div
              key={tab.id}
              className={`flex items-center space-x-3 px-4 py-3 rounded-t-xl cursor-pointer transition-all duration-200 ${
                tab.id === activeTabId
                  ? 'bg-gradient-to-b from-blue-50 to-white text-blue-600 border-b-2 border-blue-500 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50/80'
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <span className="text-sm font-medium truncate max-w-32">
                {tab.title}
              </span>
              {tab.isLoading && (
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              )}
              {tab.aiAnalyzing && (
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
              )}
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  removeTab(tab.id)
                }}
                className="p-1 hover:bg-gray-200/80 rounded-full transition-colors"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>

        {/* 现代化新建标签页按钮 */}
        <button
          onClick={() => {
            // 创建新的空白标签页，显示输入界面
            addTab({
              title: '新标签页',
              sourceType: 'text',
              sourceData: '',
              originalContent: '',
              aiNoteMarkdown: '',
              isLoading: false
            })
            setInputValue('')
          }}
          className="p-3 hover:bg-gray-100/80 rounded-xl transition-all duration-200 hover:shadow-sm"
          title="新建标签页"
        >
          <Plus size={16} className="text-gray-500" />
        </button>
      </div>

      {/* 内容区域 */}
      <div className="flex-1 overflow-hidden bg-white">
        {activeTab ? (
          activeTab.isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">正在处理内容...</p>
              </div>
            </div>
          ) : activeTab.sourceData ? (
            activeTab.sourceType === 'url' ? (
              // 对于URL类型，显示轻量级浏览器
              <LightBrowser
                url={activeTab.sourceData}
                title={activeTab.title}
                onLoadComplete={() => {
                  console.log('页面加载完成')
                }}
                onError={(error) => {
                  console.error('页面加载错误:', error)
                }}
              />
            ) : (
              // 对于文本类型，显示处理后的内容
              <div className="h-full overflow-auto">
                <div className="max-w-4xl mx-auto px-6 py-8">
                  <div className="prose prose-lg max-w-none">
                    <h1 className="text-2xl font-bold text-gray-900 mb-6">
                      {activeTab.title}
                    </h1>
                    <div
                      className="text-gray-700 leading-relaxed"
                      dangerouslySetInnerHTML={{ __html: activeTab.originalContent }}
                    />
                  </div>
                </div>
              </div>
            )
          ) : (
            // 空标签页显示输入界面
            <div className="flex items-center justify-center h-full bg-gradient-to-br from-gray-50 via-white to-gray-50">
              <div className="w-full max-w-2xl px-8">
                <div className="text-center mb-10">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl mb-4 shadow-md">
                    <span className="text-lg">✨</span>
                  </div>
                  <h2 className="text-2xl font-light text-gray-900 mb-2">开始新的探索</h2>
                  <p className="text-gray-500">输入网页链接或文本内容，生成结构化笔记</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="relative group">
                    <textarea
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder="粘贴网页链接或输入文本内容..."
                      className="w-full h-32 px-6 py-4 bg-white border border-gray-200 rounded-2xl resize-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 outline-none text-base transition-all duration-200 shadow-sm hover:shadow-md group-hover:border-gray-300"
                      disabled={useAppStore.getState().isProcessing}
                    />
                    <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                      <div className="text-xs text-gray-400">
                        {inputValue.length > 0 && `${inputValue.length} 字符`}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <button
                      type="submit"
                      disabled={!inputValue.trim() || useAppStore.getState().isProcessing}
                      className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
                    >
                      {useAppStore.getState().isProcessing ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>处理中...</span>
                        </div>
                      ) : (
                        '开始分析'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )
        ) : (
          // 没有标签页时的欢迎界面
          <div className="flex items-center justify-center h-full">
            <div className="w-full max-w-2xl px-6">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">欢迎使用沉淀</h2>
                <p className="text-lg text-gray-600 mb-8">智能内容分析与知识管理工具</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="relative">
                  <textarea
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    placeholder="粘贴网页链接或输入文本内容..."
                    className="w-full h-32 px-4 py-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={useAppStore.getState().isProcessing}
                  />
                </div>

                <button
                  type="submit"
                  disabled={!inputValue.trim() || useAppStore.getState().isProcessing}
                  className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {useAppStore.getState().isProcessing ? '处理中...' : '开始分析'}
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default WorkArea
