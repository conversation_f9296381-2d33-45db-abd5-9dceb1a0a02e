'use client'

import React, { useState, useRef, useCallback } from 'react'
import { useAppStore } from '@/lib/store'
import WorkArea from './WorkArea'
import AIAssistant from './AIAssistant'

const MainLayout: React.FC = () => {
  const { tabs } = useAppStore()
  const [leftPanelWidth, setLeftPanelWidth] = useState(65) // 默认65%
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 处理拖拽开始
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  // 处理拖拽过程
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100

    // 限制宽度范围：25% - 85%，提供更大的调节范围
    const clampedWidth = Math.max(25, Math.min(85, newLeftWidth))
    setLeftPanelWidth(clampedWidth)
  }, [isDragging])

  // 处理拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 添加全局鼠标事件监听
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  return (
    <div className="h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex flex-col">
      {/* 现代化顶部导航栏 */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/50 px-6 py-4 flex items-center justify-between flex-shrink-0 shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-md">
              <span className="text-white text-sm font-bold">沉</span>
            </div>
            <h1 className="text-xl font-light text-gray-900 tracking-wide">沉淀</h1>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {tabs.length > 0 && (
            <div className="flex items-center space-x-2 px-3 py-1.5 bg-gray-100/80 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600 font-medium">{tabs.length} 个标签页</span>
            </div>
          )}
        </div>
      </header>

      {/* 主要内容区域 */}
      <div
        ref={containerRef}
        className="flex-1 flex overflow-hidden min-h-0"
      >
        {/* 主工作区 */}
        <div
          className="flex flex-col overflow-hidden"
          style={{
            width: tabs.length > 0 ? `${leftPanelWidth}%` : '100%',
            transition: isDragging ? 'none' : 'width 0.15s ease-out'
          }}
        >
          <WorkArea />
        </div>

        {/* 可拖拽分隔条 - 只在有标签页时显示 */}
        {tabs.length > 0 && (
          <div
            className={`group flex-shrink-0 relative bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-all duration-200 ${
              isDragging ? 'bg-blue-500 shadow-md' : ''
            }`}
            onMouseDown={handleMouseDown}
            style={{
              width: isDragging ? '6px' : '4px'
            }}
          >
            {/* 拖拽手柄 */}
            <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 flex items-center justify-center">
              <div className={`w-0.5 h-12 bg-gray-500 rounded-full transition-all duration-200 ${
                isDragging ? 'bg-white h-16' : 'group-hover:bg-white group-hover:h-16'
              }`}></div>
            </div>
            
            {/* 悬浮提示 */}
            <div className={`absolute top-1/2 left-full ml-2 transform -translate-y-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap transition-opacity duration-200 pointer-events-none ${
              isDragging ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
            }`}>
              {Math.round(leftPanelWidth)}% / {Math.round(100 - leftPanelWidth)}%
            </div>
          </div>
        )}

        {/* AI助手面板 - 只在有标签页时显示 */}
        {tabs.length > 0 && (
          <div
            className="bg-white border-l border-gray-200 overflow-hidden flex flex-col"
            style={{
              width: `${100 - leftPanelWidth}%`,
              transition: isDragging ? 'none' : 'width 0.15s ease-out'
            }}
          >
            <AIAssistant />
          </div>
        )}
      </div>
    </div>
  )
}

export default MainLayout
